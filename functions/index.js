const functions = require("firebase-functions/v2");
const admin = require("firebase-admin");
admin.initializeApp();

exports.sendDailyMedicationReminders = functions.scheduler.onSchedule(
  "every 1 minutes",
  {
    timeZone: "Asia/Kolkata",
    region: "asia-south1",
  },
  async (event) => {
    const now = new Date();
    const currentTime = now.toLocaleTimeString("en-US", {
      hour: "numeric",
      minute: "2-digit",
      hour12: true,
    });

    const snapshot = await admin.firestore().collection("daily_medication").get();

    for (const doc of snapshot.docs) {
      const phone = doc.id;
      const data = doc.data();

      if (!data.medicines) continue;

      for (const med of data.medicines) {
        if (
          med.frequency === "Daily" &&
          med.timing &&
          med.timing.includes(currentTime)
        ) {
          const today = new Date().setHours(0, 0, 0, 0);
          const expiry = parseDate(med.expiry_date);
          if (expiry < today) continue;

          const userDoc = await admin.firestore().collection("users").doc(phone).get();
          const fcmToken = userDoc.exists ? userDoc.data().fcm_token : null;
          if (!fcmToken) continue;

          const message = {
            notification: {
              title: "Medication Reminder",
              body: `Time to take ${med.medicine_name} (${med.dosage})`,
            },
            token: fcmToken,
          };

          await admin.messaging().send(message);
        }
      }
    }

    return null;
  }
);

function parseDate(dateStr) {
  const [day, month, year] = dateStr.split("/").map((s) => parseInt(s));
  return new Date(year, month - 1, day).setHours(0, 0, 0, 0);
}
