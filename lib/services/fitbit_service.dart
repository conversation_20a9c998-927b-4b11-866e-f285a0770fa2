import 'dart:convert';
import 'dart:developer';
import 'dart:math' as math;
import 'package:crypto/crypto.dart';
import 'package:http/http.dart' as http;
import 'package:oauth2/oauth2.dart' as oauth2;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:url_launcher/url_launcher.dart';
import '../models/fitbit_models.dart';
import '../config/fitbit_config.dart';
import 'secure_storage_service.dart';

/// Service for handling FitBit API integration with OAuth 2.0 + PKCE
class FitBitService {
  static const String _baseUrl = FitBitConfig.baseUrl;
  static const String _authUrl = FitBitConfig.authUrl;
  static const String _tokenUrl = FitBitConfig.tokenUrl;

  static const String _tokenKey = 'fitbit_tokens';
  static const String _userKey = 'fitbit_user';

  oauth2.Client? _client;
  FitBitTokens? _tokens;
  FitBitUser? _user;

  /// Get current connection status
  FitBitConnectionStatus get connectionStatus {
    if (_tokens == null) return FitBitConnectionStatus.disconnected;
    if (_tokens!.isExpired) return FitBitConnectionStatus.tokenExpired;
    return FitBitConnectionStatus.connected;
  }

  /// Get current user
  FitBitUser? get currentUser => _user;

  /// Check if user is authenticated
  bool get isAuthenticated => connectionStatus == FitBitConnectionStatus.connected;

  /// Initialize the service and restore saved tokens
  Future<void> initialize() async {
    try {
      await _loadSavedTokens();
      await _loadSavedUser();

      if (_tokens != null && !_tokens!.isExpired) {
        await _createClientFromTokens();
        log('FitBit service initialized with saved tokens');
      } else if (_tokens != null && _tokens!.isExpired) {
        log('FitBit tokens expired, need to refresh');
        await refreshTokens();
      }
    } catch (e) {
      log('Error initializing FitBit service: $e');
    }
  }

  /// Start OAuth authentication flow
  Future<bool> authenticate() async {
    try {
      log('Starting FitBit OAuth authentication');

      // Check if FitBit is properly configured
      if (!FitBitConfig.isConfigured) {
        log('FitBit credentials not configured');
        return false;
      }

      // Generate PKCE parameters
      final codeVerifier = _generateCodeVerifier();
      final codeChallenge = _generateCodeChallenge(codeVerifier);

      // Store code verifier for later use in callback
      await SecureStorageService.storeFitbitCodeVerifier(codeVerifier);

      // Build authorization URL
      final authUri = Uri.parse(_authUrl).replace(queryParameters: {
        'client_id': FitBitAuthConfig.defaultConfig.clientId,
        'response_type': 'code',
        'code_challenge': codeChallenge,
        'code_challenge_method': 'S256',
        'redirect_uri': FitBitAuthConfig.defaultConfig.redirectUri,
        'scope': FitBitAuthConfig.defaultConfig.scopes.join(' '),
        'state': _generateState(),
      });

      // Launch browser for authentication
      if (await canLaunchUrl(authUri)) {
        await launchUrl(authUri, mode: LaunchMode.externalApplication);
        return true;
      } else {
        log('Cannot launch FitBit auth URL');
        return false;
      }
    } catch (e) {
      log('Error starting FitBit authentication: $e');
      return false;
    }
  }

  /// Handle OAuth callback with authorization code
  Future<bool> handleAuthCallback(String authorizationCode, String codeVerifier) async {
    try {
      log('Handling FitBit OAuth callback');

      // Exchange authorization code for tokens
      final response = await http.post(
        Uri.parse(_tokenUrl),
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Authorization': 'Basic ${_getBasicAuthHeader()}',
        },
        body: {
          'client_id': FitBitAuthConfig.defaultConfig.clientId,
          'grant_type': 'authorization_code',
          'code': authorizationCode,
          'redirect_uri': FitBitAuthConfig.defaultConfig.redirectUri,
          'code_verifier': codeVerifier,
        },
      );

      if (response.statusCode == 200) {
        final tokenData = json.decode(response.body);
        _tokens = FitBitTokens.fromJson(tokenData);

        await _saveTokens();
        await _createClientFromTokens();
        await _fetchAndSaveUser();

        log('FitBit authentication successful');
        return true;
      } else {
        log('FitBit token exchange failed: ${response.statusCode} - ${response.body}');
        return false;
      }
    } catch (e) {
      log('Error handling FitBit auth callback: $e');
      return false;
    }
  }

  /// Refresh expired tokens
  Future<bool> refreshTokens() async {
    if (_tokens?.refreshToken == null) return false;

    try {
      log('Refreshing FitBit tokens');

      final response = await http.post(
        Uri.parse(_tokenUrl),
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Authorization': 'Basic ${_getBasicAuthHeader()}',
        },
        body: {
          'grant_type': 'refresh_token',
          'refresh_token': _tokens!.refreshToken,
        },
      );

      if (response.statusCode == 200) {
        final tokenData = json.decode(response.body);
        _tokens = FitBitTokens.fromJson(tokenData);

        await _saveTokens();
        await _createClientFromTokens();

        log('FitBit tokens refreshed successfully');
        return true;
      } else {
        log('FitBit token refresh failed: ${response.statusCode} - ${response.body}');
        await disconnect();
        return false;
      }
    } catch (e) {
      log('Error refreshing FitBit tokens: $e');
      return false;
    }
  }

  /// Disconnect and clear all stored data
  Future<void> disconnect() async {
    try {
      _client = null;
      _tokens = null;
      _user = null;

      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_tokenKey);
      await prefs.remove(_userKey);

      log('FitBit disconnected successfully');
    } catch (e) {
      log('Error disconnecting FitBit: $e');
    }
  }

  /// Fetch user profile
  Future<FitBitUser?> fetchUser() async {
    if (!isAuthenticated) return null;

    try {
      final response = await _makeAuthenticatedRequest('/1/user/-/profile.json');
      if (response != null) {
        _user = FitBitUser.fromJson(response);
        await _saveUser();
        return _user;
      }
    } catch (e) {
      log('Error fetching FitBit user: $e');
    }
    return null;
  }

  /// Fetch activity data for a specific date
  Future<FitBitActivityData?> fetchActivityData(DateTime date) async {
    if (!isAuthenticated) return null;

    try {
      final dateStr = _formatDate(date);
      final response = await _makeAuthenticatedRequest('/1/user/-/activities/date/$dateStr.json');

      if (response != null) {
        return FitBitActivityData.fromJson(response, date);
      }
    } catch (e) {
      log('Error fetching FitBit activity data: $e');
    }
    return null;
  }

  /// Fetch heart rate data for a specific date
  Future<FitBitHeartRateData?> fetchHeartRateData(DateTime date) async {
    if (!isAuthenticated) return null;

    try {
      final dateStr = _formatDate(date);
      final response = await _makeAuthenticatedRequest('/1/user/-/activities/heart/date/$dateStr/1d.json');

      if (response != null) {
        return FitBitHeartRateData.fromJson(response, date);
      }
    } catch (e) {
      log('Error fetching FitBit heart rate data: $e');
    }
    return null;
  }

  /// Fetch sleep data for a specific date
  Future<FitBitSleepData?> fetchSleepData(DateTime date) async {
    if (!isAuthenticated) return null;

    try {
      final dateStr = _formatDate(date);
      final response = await _makeAuthenticatedRequest('/1.2/user/-/sleep/date/$dateStr.json');

      if (response != null) {
        return FitBitSleepData.fromJson(response, date);
      }
    } catch (e) {
      log('Error fetching FitBit sleep data: $e');
    }
    return null;
  }

  /// Fetch comprehensive data for a specific date
  Future<FitBitDayData?> fetchDayData(DateTime date) async {
    if (!isAuthenticated) return null;

    try {
      final activity = await fetchActivityData(date);
      final heartRate = await fetchHeartRateData(date);
      final sleep = await fetchSleepData(date);

      return FitBitDayData(
        date: date,
        activity: activity,
        heartRate: heartRate,
        sleep: sleep,
      );
    } catch (e) {
      log('Error fetching FitBit day data: $e');
      return null;
    }
  }

  /// Make authenticated API request
  Future<Map<String, dynamic>?> _makeAuthenticatedRequest(String endpoint) async {
    if (_client == null) return null;

    try {
      // Check if token needs refresh
      if (_tokens?.isExpiringSoon == true) {
        await refreshTokens();
      }

      final response = await _client!.get(Uri.parse('$_baseUrl$endpoint'));

      if (response.statusCode == 200) {
        return json.decode(response.body);
      } else if (response.statusCode == 401) {
        log('FitBit API unauthorized, attempting token refresh');
        if (await refreshTokens()) {
          // Retry the request with new token
          final retryResponse = await _client!.get(Uri.parse('$_baseUrl$endpoint'));
          if (retryResponse.statusCode == 200) {
            return json.decode(retryResponse.body);
          }
        }
      }

      log('FitBit API request failed: ${response.statusCode} - ${response.body}');
    } catch (e) {
      log('Error making FitBit API request: $e');
    }
    return null;
  }

  /// Create OAuth client from stored tokens
  Future<void> _createClientFromTokens() async {
    if (_tokens == null) return;

    final credentials = oauth2.Credentials(
      _tokens!.accessToken,
      refreshToken: _tokens!.refreshToken,
      tokenEndpoint: Uri.parse(_tokenUrl),
      scopes: _tokens!.scopes,
      expiration: _tokens!.expiresAt,
    );

    _client = oauth2.Client(
      credentials,
      identifier: FitBitAuthConfig.defaultConfig.clientId,
      secret: FitBitAuthConfig.defaultConfig.clientSecret,
    );
  }

  /// Generate PKCE code verifier
  String _generateCodeVerifier() {
    final random = math.Random.secure();
    final bytes = List<int>.generate(32, (i) => random.nextInt(256));
    return base64Url.encode(bytes).replaceAll('=', '');
  }

  /// Generate PKCE code challenge
  String _generateCodeChallenge(String verifier) {
    final bytes = utf8.encode(verifier);
    final digest = sha256.convert(bytes);
    return base64Url.encode(digest.bytes).replaceAll('=', '');
  }

  /// Generate random state parameter
  String _generateState() {
    final random = math.Random.secure();
    final bytes = List<int>.generate(16, (i) => random.nextInt(256));
    return base64Url.encode(bytes).replaceAll('=', '');
  }

  /// Get Basic Auth header for client credentials
  String _getBasicAuthHeader() {
    final credentials = '${FitBitAuthConfig.defaultConfig.clientId}:${FitBitAuthConfig.defaultConfig.clientSecret}';
    return base64.encode(utf8.encode(credentials));
  }

  /// Format date for API requests
  String _formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  /// Load saved tokens from SharedPreferences
  Future<void> _loadSavedTokens() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final tokenJson = prefs.getString(_tokenKey);

      if (tokenJson != null) {
        final tokenData = json.decode(tokenJson);
        _tokens = FitBitTokens.fromJson(tokenData);
      }
    } catch (e) {
      log('Error loading saved FitBit tokens: $e');
    }
  }

  /// Save tokens to SharedPreferences
  Future<void> _saveTokens() async {
    if (_tokens == null) return;

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_tokenKey, json.encode(_tokens!.toJson()));
    } catch (e) {
      log('Error saving FitBit tokens: $e');
    }
  }

  /// Load saved user from SharedPreferences
  Future<void> _loadSavedUser() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userJson = prefs.getString(_userKey);

      if (userJson != null) {
        final userData = json.decode(userJson);
        _user = FitBitUser.fromJson(userData);
      }
    } catch (e) {
      log('Error loading saved FitBit user: $e');
    }
  }

  /// Save user to SharedPreferences
  Future<void> _saveUser() async {
    if (_user == null) return;

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_userKey, json.encode(_user!.toJson()));
    } catch (e) {
      log('Error saving FitBit user: $e');
    }
  }

  /// Fetch and save user profile
  Future<void> _fetchAndSaveUser() async {
    final user = await fetchUser();
    if (user != null) {
      _user = user;
      await _saveUser();
    }
  }
}
